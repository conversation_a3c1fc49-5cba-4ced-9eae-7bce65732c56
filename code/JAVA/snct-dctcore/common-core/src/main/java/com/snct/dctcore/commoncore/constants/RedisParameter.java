package com.snct.dctcore.commoncore.constants;

/**
 * @description: 所有参数整合
 * @author: snct
 **/

public class RedisParameter {
    /*
     * @description: UDP消息接收参数
     */


    /**
     * 记录不同船，pazu的ip和port,key中后面带上sn号
     */
    public static String SHIP_PAZU_IP = "SHIP_PAZU_IP_";
    public static String SHIP_PAZU_PORT = "SHIP_PAZU_PORT_";

    /**
     * 按数据类型存储，拆包的未合并之前临时存储
     */
    public static String SHORE_RECEIVE_CODE_TEMPORARY = "SHORE_RECEIVE_CODE_TEMPORARY_";

    /**
     * 存储接收到的数据
     */
    public static String SHORE_RECEIVE_DATA = "SHORE_RECEIVE_DATA-";

    /**
     * 拆包数据，最后存储时间
     */
    public static String SHORE_UNPACKING_NEWEST_TIME = "SHORE_UNPACKING_NEWEST_TIME_";

    /**
     * 岸上丢失数据集合
     */
    public static String SHORE_LOSE_DATA_SET = "SHORE_LOSE_DATA_SET";

    /**
     * 图片最新数据
     */
    public static String SHORE_UNPACKING_NEWEST_DATA = "SHORE_UNPACKING_NEWEST_DATA_";


    /**
     * 最大接收到的编号--new
     */
    public static String MAX_RECEIVE_NUM = "MAX_RECEIVE_NUM-";
    /**
     * 最大校验编号--new
     */
    public static String MAX_CHECK_NUM = "MAX_CHECK_NUM-";
    /**
     * 岸上丢失数据链表--new
     */
    public static String LOSE_DATA_LIST = "LOSE_DATA_LIST-";
    /**
     * 最大接收编号与最大校验编号的差--new
     */
    public static String CHECK_INTERVAL_NUM = "CHECK_INTERVAL_NUM-";
    /**
     * 所有启用的船只sn号
     */
    public static String ALL_ENABLE_SHIP_SN = "ALL_ENABLE_SHIP_SN";

    /**
     * 最大接收编号
     */
    public static String SHORE_MAX_RECEIVE_NUM = "SHORE_MAX_RECEIVE_NUM";

    /**
     * 最大校验编号
     */
    public static String SHORE_MAX_CHECK_NUM = "SHORE_MAX_CHECK_NUM";

    /**
     * 最大接收编号与最大校验编号的差
     */
    public static String SHORE_CHECK_INTERVAL_NUM = "SHORE_CHECK_INTERVAL_NUM";

    /*
     * @description:UDP丢包补数据服务
     */
    /**
     * 岸上丢失数据集合--已发送过一次(记录起来，等N分钟后再发)
     */
    public static String SHORE_LOSE_DATA_NUM_HASH = "SHORE_LOSE_DATA_NUM_HASH";

    /**
     * redis数据最新时间
     */
    public static String LATEST_DATA = "LATEST_DATA_";

    /**
     * redis数据最新时间
     */
    public static String LATEST_DATE = "LATEST_DATE_";

    /**
     * 单位时间内接收到的数据的数量，丢失重传的数量
     */
    public static String DATA_AMOUNT = "DATA_AMOUNT-";




//---------------------------------------------数据接收日志----------------------------------------------------------------
    /**
     * 一秒钟接收数据总长度
     */
    public static String RECEIVE_TOTAL_LENGTH_S = "RECEIVE_TOTAL_LENGTH_S-";
    /**
     * 一秒钟接收数据总条数
     */
    public static String RECEIVE_LINES_S = "RECEIVE_LINES_S-";
    /**
     * 一秒钟接收补数据总长度
     */
    public static String RECEIVE_REPAIR_LENGTH_S = "RECEIVE_REPAIR_LENGTH_S-";
    /**
     * 一秒钟接收图片数据长度
     */
    public static String RECEIVE_PIC_LENGTH_S = "RECEIVE_PIC_LENGTH_S-";

    /**
     * 一分钟接收数据总长度
     */
    public static String RECEIVE_TOTAL_LENGTH_M = "RECEIVE_TOTAL_LENGTH_M-";
    /**
     * 一分钟接收数据总条数
     */
    public static String RECEIVE_LINES_M = "RECEIVE_LINES_M-";
    /**
     * 一分钟接收补数据总长度
     */
    public static String RECEIVE_REPAIR_LENGTH_M = "RECEIVE_REPAIR_LENGTH_M-";
    /**
     * 一分钟接收图片数据长度
     */
    public static String RECEIVE_PIC_LENGTH_M = "RECEIVE_PIC_LENGTH_M-";
    /**
     * 一分钟接收数据长度,是否已保存
     */
    public static String RECEIVE_DATA_LENGTH_SAVE = "RECEIVE_DATA_LENGTH_SAVE-";

    /**
     * 最新接收图片的时间
     */
    public static String LATEST_PICTURE_DATE = "LATEST_PICTURE_DATE-";

//---------------------------------------------数据接收日志----------------------------------------------------------------



//---------------------------------------------台风----------------------------------------------------------------

    /**
     * 台风列表
     */
    public static String TYPHOON_LIST = "TYPHOON:List-";
    /**
     * 台风信息列表
     */
    public static String TYPHOON_INFO = "TYPHOON:INFO-";

    /**
     * 活跃台风摘要信息
     */
    public static final String TYPHOON_ACTIVE_SUMMARY = "TYPHOON:ACTIVE";

//---------------------------------------------台风----------------------------------------------------------------

//---------------------------------------------机舱数据----------------------------------------------------------------

    /**
     * 机舱数据接收时间
     */
    public static String ENGINE_ROOM_DATE = "ENGINE_ROOM_DATE";

//---------------------------------------------机舱数据----------------------------------------------------------------
}
